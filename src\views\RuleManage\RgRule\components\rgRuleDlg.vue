<template>
  <!-- 人工规则弹窗 -->
  <div class="rg-rule-dlg-wrap">
    <el-dialog
      :title="edit ? t('common.edit') : t('common.add')"
      v-model="handleRule"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleFormRef"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item v-if="edit" label="审核项编号：">
          <span>{{ auditServerNo }}</span>
        </el-form-item>
        <el-form-item label="审核项：" prop="ruleName">
          <el-input
            placeholder="请输入审核项"
            clearable
            v-model.trim="ruleForm.ruleName"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="分类：" prop="categoryId">
          <el-select width="100%" v-model="ruleForm.categoryId" placeholder="请选择分类 ">
            <el-option
              v-for="item in ruleClassList"
              :key="item.categoryId"
              :label="item.categoryName"
              :value="item.categoryId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="描述：" prop="remark">
          <el-input
            placeholder="请输入描述"
            clearable
            v-model.trim="ruleForm.remark"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="返回原因：" prop="showReason">
          <el-input
            placeholder="请输入返回原因"
            clearable
            v-model.trim="ruleForm.showReason"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div>
        <!-- 按钮 -->
        <div style="text-align: center">
          <el-button @click="handleClose">{{ t('common.cancel') }}</el-button>
          <el-button type="primary" :disabled="submit" @click="submitForm(ruleFormRef)">{{
            t('common.ok')
          }}</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
const { t } = useI18n()
import { ref, toRefs, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
// import $http from '@/utils/request'
import { addRgRuleInfoApi, editRgRuleInfoApi } from '@/api/RuleManage'
import useMessage from '@/utils/useMessage'

import { object } from 'vue-types'
const message = useMessage()
// 获取路由器实例
const router = useRouter()
// route响应式对象，监控变化，传值
const route = useRoute()

/** 接收父级传入的参数 start */
const props = defineProps({
  handleRule: {
    type: Boolean,
    default: false, //默认值
    required: true //是否必传
  },
  edit: {
    type: Boolean,
    default: false, //默认值
    required: true //是否必传
  },
  ruleInfo: {
    type: Object,
    default: () => {}, //默认值
    required: false //是否必传
  },
  ruleClassList: {
    type: Array,
    default: () => [], //默认值
    required: false //是否必传
  }
})
// 解构传入的弹窗开关
const { handleRule, edit, ruleInfo } = toRefs(props)
// 接收关闭弹窗自定义方法
const emits = defineEmits(['closeDialog'])
// 关闭删除角色弹窗
const handleClose = (done) => {
  emits('closeDialog')
}
const auditServerNo = ref('') //审核项编号
const artificialId = ref('') //审核项主键
/** 接收父级传入的参数 end */

/**  loading加载开始  start */
const loading = ref(false)
const pending = ref([])
const cancelLoading = (add) => {
  for (const p in pending.value) {
    if (pending.value[p].conf === add) {
      pending.value.splice(p, 1) // 把这条记录从数组中移除
    }
  }
  loading.value = pending.value.length > 0
}
const addLoading = (add) => {
  loading.value = true
  pending.value.push({ conf: add })
}
/**  loading加载开始  end */

const submit = ref(false)
const ruleFormRef = ref() // 表单校验
const ruleForm = reactive({
  ruleForm: {
    ruleName: '', // 审核项
    categoryId: '', //分类
    remark: '', // 描述
    showReason: '' //返回原因
  },
  arithmeticList: []
})
const rules = reactive({
  categoryId: [{ required: true, message: '请选择分类' }],
  ruleName: [
    { required: true, message: '请填写审核项' },
    { min: 1, max: 20, message: '长度在 1 到 20 个字符' }
  ],
  remark: [{ min: 1, max: 200, message: '长度在 1 到 200 个字符' }],
  showReason: [
    { required: true, message: '请填写返回原因' },
    { min: 1, max: 20, message: '长度在 1 到 20 个字符' }
  ]
})

onMounted(() => {
  if (edit.value) {
    ruleForm.categoryId = ruleInfo.value.categoryId
    ruleForm.remark = ruleInfo.value.remark
    ruleForm.ruleName = ruleInfo.value.ruleName
    ruleForm.showReason = ruleInfo.value.showReason
    auditServerNo.value = ruleInfo.value.auditServerNo
    artificialId.value = ruleInfo.value.artificialId
  }
})

// 提交校验
const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      if (edit.value) {
        editRgRuleInfo()
      } else {
        addRgRuleInfo()
      }
    } else {
      return false
    }
  })
}

// 新增人工规则
// const addRgRuleInfo = () => {
//   let data = {
//     ruleName: ruleForm.ruleName,
//     categoryId: ruleForm.categoryId,
//     showReason: ruleForm.showReason,
//     remark: ruleForm.remark
//   }
//   submit.value = true
//   $http
//     .post({
//       url: `/review/artificialRule/save`,
//       data,
//       router,
//       type: 'json'
//     })
//     .then((res) => {
//       if (res.code == 0) {
//         message.success('创建成功')
//         emits('closeDialog', true)
//       }
//     })
//     .catch(() => {})
//     .finally(() => {
//       submit.value = false
//     })
// }

// 新增人工规则
const addRgRuleInfo = async () => {
  const data = {
    ruleName: ruleForm.ruleName,
    categoryId: ruleForm.categoryId,
    showReason: ruleForm.showReason,
    remark: ruleForm.remark
  }
  submit.value = true
  try {
    const res = await addRgRuleInfoApi(data)
    if (res?.code == 0) {
      message.success('创建成功')
      emits('closeDialog', true)
    }
  } finally {
    submit.value = false
  }
}
// 编辑人工规则
// const editRgRuleInfo = () => {
//   let data = {
//     categoryName: ruleForm.categoryName,
//     remark: ruleForm.remark,
//     showReason: ruleForm.showReason,
//     ruleName: ruleForm.ruleName,
//     categoryId: ruleForm.categoryId,
//     auditServerNo: auditServerNo.value,
//     artificialId: artificialId.value
//   }
//   submit.value = true
//   $http
//     .post({
//       url: `/review/artificialRule/update`,
//       data,
//       router,
//       type: 'json'
//     })
//     .then((res) => {
//       if (res.code == 0) {
//         message.success('修改成功')
//         emits('closeDialog', true)
//       }
//     })
//     .catch(() => {})
//     .finally(() => {
//       submit.value = false
//     })
// }

// 编辑人工规则
const editRgRuleInfo = async () => {
  const data = {
    categoryName: ruleForm.categoryName,
    remark: ruleForm.remark,
    showReason: ruleForm.showReason,
    ruleName: ruleForm.ruleName,
    categoryId: ruleForm.categoryId,
    auditServerNo: auditServerNo.value,
    artificialId: artificialId.value
  }
  submit.value = true
  try {
    const res = await editRgRuleInfoApi(data)
    if (res?.code == 0) {
      message.success('修改成功')
      emits('closeDialog', true)
    }
  } finally {
    submit.value = false
  }
}
</script>

<style scoped lang="less">
.rg-rule-dlg-wrap {
  // font-size: 13px;
  :deep(.el-dialog) {
    min-width: 720px;
  }

  :deep(.el-textarea__inner) {
    padding-right: 55px;
  }
}
.el-form-item__threshold {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  top: 100%;
  left: 0;
}
</style>
