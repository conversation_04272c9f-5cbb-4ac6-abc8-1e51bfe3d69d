<template>
  <!-- 分类弹窗 -->
  <div class="sort-dlg-wrap">
    <el-dialog
      :title="edit ? '编辑分类' : '新增分类'"
      v-model="handleRule"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleFormRef"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="分类名称：" prop="categoryName">
          <el-input
            v-model="ruleForm.categoryName"
            placeholder="请输入分类名称"
            clearable
            maxlength="40"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="描述：" prop="remark">
          <el-input
            placeholder="请输入描述"
            clearable
            v-model="ruleForm.remark"
            maxlength="200"
            type="textarea"
            :rows="4"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div>
        <!-- 按钮 -->
        <div style="text-align: center">
          <el-button @click="handleClose">{{ t('common.cancel') }}</el-button>
          <el-button type="primary" :disabled="submit" @click="submitForm(ruleFormRef)">{{
            t('common.ok')
          }}</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
const { t } = useI18n()
import { ref, toRefs, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
// import $http from '@/utils/request'
import useMessage from '@/utils/useMessage'

import { object } from 'vue-types'
import { addSortInfoApi, editSortInfoApi } from '@/api/RuleManage'
const message = useMessage()
// 获取路由器实例
const router = useRouter()
// route响应式对象，监控变化，传值
const route = useRoute()

/** 接收父级传入的参数 start */
const props = defineProps({
  handleRule: {
    type: Boolean,
    default: false, //默认值
    required: true //是否必传
  },
  edit: {
    type: Boolean,
    default: false, //默认值
    required: true //是否必传
  },
  dataInfo: {
    type: Object,
    default: () => {}, //默认值
    required: false //是否必传
  }
})
// 解构传入的参数
const { handleRule, edit, dataInfo } = toRefs(props)
// 接收关闭弹窗自定义方法
const emits = defineEmits(['closeDialog'])
// 关闭删除角色弹窗
const handleClose = (done) => {
  emits('closeDialog')
}
const categoryId = ref('') // 分类Id

/** 接收父级传入的参数 end */

/**  loading加载开始  start */
const loading = ref(false)
const pending = ref([])
const cancelLoading = (add) => {
  for (const p in pending.value) {
    if (pending.value[p].conf === add) {
      pending.value.splice(p, 1) // 把这条记录从数组中移除
    }
  }
  loading.value = pending.value.length > 0
}
const addLoading = (add) => {
  loading.value = true
  pending.value.push({ conf: add })
}
/**  loading加载开始  end */

const submit = ref(false)
const ruleFormRef = ref() // 表单校验
const ruleForm = reactive({
  categoryName: '', // 分类名称
  remark: '' // 说明
})
const rules = reactive({
  categoryName: [
    { required: true, message: '请填写分类名称' },
    { min: 1, max: 40, message: '长度在 1 到 40 个字符', trigger: 'blur' }
  ],
  remark: [{ min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' }]
})

onMounted(() => {
  if (edit.value) {
    ruleForm.categoryName = dataInfo.value.categoryName // 分类名称
    ruleForm.remark = dataInfo.value.remark // 说明
    categoryId.value = dataInfo.value.categoryId
  }
})

// 提交校验
const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      if (edit.value) {
        editSortInfo()
      } else {
        addSortInfo()
      }
    } else {
      return false
    }
  })
}

// 新增审核项分类
// const addSortInfo2 = () => {
//   let data = {
//     categoryName: ruleForm.categoryName,
//     remark: ruleForm.remark
//   }
//   submit.value = true
//   $http
//     .post({
//       url: `/review/ruleCategory/save`,
//       data,
//       router,
//       type: 'json'
//     })
//     .then((res) => {
//       if (res.code == 0) {
//         message.success('新增成功')
//         emits('closeDialog', true)
//       }
//     })
//     .catch(() => {})
//     .finally(() => {
//       submit.value = false
//     })
// }

// 新增审核项分类
const addSortInfo = async () => {
  const data = {
    categoryName: ruleForm.categoryName,
    remark: ruleForm.remark
  }
  submit.value = true
  try {
    const res = await addSortInfoApi(data)
    if (res.code == 0) {
      message.success('新增成功')
      emits('closeDialog', true)
    }
  } finally {
    submit.value = false
  }
}

// 编辑审核项分类
// const editSortInfo2 = () => {
//   let data = {
//     categoryName: ruleForm.categoryName,
//     remark: ruleForm.remark,
//     categoryId: categoryId.value
//   }
//   submit.value = true
//   $http
//     .post({
//       url: `/review/ruleCategory/update`,
//       data,
//       router,
//       type: 'json'
//     })
//     .then((res) => {
//       if (res.code == 0) {
//         message.success('更新成功')
//         emits('closeDialog', true)
//       }
//     })
//     .catch(() => {})
//     .finally(() => {
//       submit.value = false
//     })
// }

// 编辑审核项分类
const editSortInfo = async () => {
  const data = {
    categoryName: ruleForm.categoryName,
    remark: ruleForm.remark,
    categoryId: categoryId.value
  }
  submit.value = true
  try {
    const res = await editSortInfoApi(data)
    if (res.code == 0) {
      message.success('更新成功')
      emits('closeDialog', true)
    }
  } finally {
    submit.value = false
  }
}
</script>

<style scoped lang="less">
.sort-dlg-wrap {
  // font-size: 13px;
  :deep(.el-dialog) {
    min-width: 720px;
  }

  :deep(.el-textarea__inner) {
    padding-right: 55px;
  }
}
.el-form-item__threshold {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  top: 100%;
  left: 0;
}
</style>
