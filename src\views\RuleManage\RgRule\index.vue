<!-- eslint-disable vue/v-on-event-hyphenation -->
<template>
  <!-- 人工规则管理 -->
  <UmvContent>
    <!-- 查询条件 -->
    <UmvQuery
      v-model="searchData"
      :opts="queryOpts"
      label-width="100px"
      @check="handleSearch"
      @reset="handleReset"
    />

    <!-- 表格 -->
    <UmvTable v-loading="loading" :data="tableData" :columns="columns" @refresh="queryRgRoleList">
      <!-- 工具栏 -->
      <template #tools>
        <el-button type="primary" size="small" v-track:click.btn @click="openDialog('add')">
          {{ t('common.add') }}
        </el-button>
      </template>

      <!-- 分页 -->
      <template #pagination>
        <Pagination
          :current-page="query.pageNo"
          :page-size="query.pageSize"
          :total="query.total"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </template>
    </UmvTable>

    <!-- 编辑弹窗 -->
    <rgRuleDlg
      v-if="handleRule"
      :handleRule="handleRule"
      :edit="edit"
      :ruleClassList="ruleClassList"
      :ruleInfo="ruleInfo"
      @closeDialog="closeDialog"
    />
    <!-- 删除弹窗 -->
    <deleteRgRoleDlg
      v-if="deleteRule"
      :deleteRule="deleteRule"
      :dataInfo="ruleInfo"
      @closeDialog="closeDialog"
    />
  </UmvContent>
</template>

<script setup lang="tsx">
import { ref, reactive, onMounted } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
import { ElInput, ElSelect, ElOption, ElButton } from 'element-plus'

// 组件导入
import UmvContent from '@/components/UmvContent'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import rgRuleDlg from './components/rgRuleDlg.vue'
import deleteRgRoleDlg from './components/deleteRgRoleDlg.vue'

// API 和工具函数
import { getCategoryList, getRgRoleList } from '@/api/RuleManage'

// 定义组件名称
defineOptions({
  name: 'RgRule'
})

const { t } = useI18n()

const loading = ref(false)
const handleRule = ref(false) // 新增审核项弹窗
const deleteRule = ref(false) // 删除弹窗
const edit = ref(false) // 是否为编辑
const ruleClassList = ref([]) // 分类列表
const tableData = ref([]) //  人工规则列表
const query = reactive({
  total: 0,
  pageNo: 1,
  pageSize: 10
})
const searchData = ref({
  auditServerNo: '', // 规则编号
  ruleName: '', // 审核项
  categoryId: '' // 分类
})
const ruleInfo = ref<any>({}) // 规则信息

// 查询配置
const queryOpts = ref<Record<string, QueryOption>>({
  categoryId: {
    label: '分类',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElSelect v-model={form.categoryId} clearable placeholder="请选择分类" style="width: 100%">
        {ruleClassList.value.map((item: any) => (
          <ElOption key={item.categoryId} label={item.categoryName} value={item.categoryId} />
        ))}
      </ElSelect>
    )
  },
  ruleName: {
    label: '审核项',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElInput
        v-model={form.ruleName}
        maxlength="40"
        clearable
        placeholder="请输入审核项"
        style="width: 100%"
      />
    )
  },
  auditServerNo: {
    label: '审核项编号',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElInput
        v-model={form.auditServerNo}
        maxlength="10"
        clearable
        placeholder="请输入服务编号"
        style="width: 100%"
      />
    )
  }
})

// 表格列配置
const columns = ref<TableColumn[]>([
  {
    prop: 'index',
    label: '序号',
    width: '60px',
    renderTemplate: (scope) => <span>{scope.$index + 1}</span>
  },
  { prop: 'ruleName', label: '审核项', minWidth: '120px' },
  { prop: 'categoryName', label: '分类', minWidth: '100px' },
  { prop: 'remark', label: '描述', minWidth: '200px' },
  { prop: 'auditServerNo', label: '审核项编号', width: '100px' },
  {
    prop: 'operation',
    label: '操作',
    fixed: 'right',
    width: '110px',
    renderTemplate: (scope) => (
      <div>
        <ElButton
          type="primary"
          link
          v-track:click_btn
          onClick={() => openDialog('edit', scope.row)}
        >
          {t('common.edit')}
        </ElButton>
        <ElButton
          type="danger"
          link
          v-track:click_btn
          onClick={() => openDialog('delete', scope.row)}
        >
          {t('common.delete')}
        </ElButton>
      </div>
    )
  }
])

// 获取分类列表
const queryCategoryList = async () => {
  const data = {
    categoryName: '',
    pageNum: 1,
    pageSize: 99999
  }
  loading.value = true
  try {
    const resData = await getCategoryList(data)
    ruleClassList.value = resData?.list || []
  } finally {
    loading.value = false
  }
}

// 获取人工规则列表
const queryRgRoleList = async () => {
  const data = {
    auditServerNo: searchData.value.auditServerNo, // 规则编号
    ruleName: searchData.value.ruleName, // 审核项
    categoryId: searchData.value.categoryId, //分类编号
    pageNum: query.pageNo,
    pageSize: query.pageSize
  }
  loading.value = true
  try {
    const resData = await getRgRoleList(data)
    tableData.value = resData?.list || []
    query.total = resData?.total || 0
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  queryRgRoleList()
  queryCategoryList()
})

// 打开添加规则弹窗
const openDialog = (type: string, obj?: any) => {
  console.log(obj, type)
  switch (type) {
    // 新增
    case 'add':
      handleRule.value = true
      edit.value = false
      break
    // 编辑
    case 'edit':
      edit.value = true
      ruleInfo.value = obj
      handleRule.value = true
      break
    // 删除
    case 'delete':
      ruleInfo.value = obj
      deleteRule.value = true
      break
  }
}
// 关闭添加规则弹窗
const closeDialog = (result?: any) => {
  edit.value = false
  handleRule.value = false
  deleteRule.value = false
  if (result) {
    queryRgRoleList()
  }
}

// 搜索
const handleSearch = () => {
  queryRgRoleList()
}
// 重置
const handleReset = () => {
  searchData.value.categoryId = ''
  searchData.value.auditServerNo = ''
  searchData.value.ruleName = ''
  query.pageNo = 1
  queryRgRoleList()
}

// 改变页数
const handlePageChange = (val: number) => {
  query.pageNo = val
  queryRgRoleList()
}
// 改变条数
const handleSizeChange = (val: number) => {
  query.pageNo = 1
  query.pageSize = val
  queryRgRoleList()
}
</script>
