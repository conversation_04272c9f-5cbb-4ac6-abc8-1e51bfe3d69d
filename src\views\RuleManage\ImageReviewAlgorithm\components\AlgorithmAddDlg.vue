<!-- eslint-disable vue/require-valid-default-prop -->
<template>
  <div class="ai-rules-dlg-wrap">
    <el-dialog
      :title="edit ? t('common.edit') : t('common.add')"
      v-model="handleRule"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <el-form
        :model="algorithmForm"
        :rules="rules"
        ref="algorithmFormRef"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item v-if="edit" label="算法ID：">
          <span>{{ algorithmForm.algorithmId }}</span>
        </el-form-item>
        <el-form-item label="类型：" prop="algorithmType">
          <el-select
            width="100%"
            v-model="algorithmForm.algorithmType"
            placeholder="请选择类型"
            @change="handleChange"
            disabled
          >
            <el-option
              v-for="item in algorithmTypeList"
              :key="item.typeKey"
              :label="item.typeValue"
              :value="item.typeKey"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="算法名称：" prop="algorithmName">
          <el-input
            placeholder="请填写算法名称"
            clearable
            v-model.trim="algorithmForm.algorithmName"
            maxlength="40"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="描述：" prop="algorithmDesc">
          <el-input
            placeholder="请填写描述"
            clearable
            v-model.trim="algorithmForm.algorithmDesc"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        <el-form-item
          v-if="algorithmForm.algorithmType == 'handle'"
          label="算法编号："
          prop="algorithmNo"
        >
          <el-select width="100%" v-model="algorithmForm.algorithmNo" placeholder="请选择算法编号">
            <el-option
              v-for="item in aiHandleList"
              :key="item.algorithmNo"
              :label="item.algorithmName"
              :value="item.algorithmNo"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-else label="算法编号：" prop="algorithmNo">
          <el-select width="100%" v-model="algorithmForm.algorithmNo" placeholder="请选择算法编号">
            <el-option
              v-for="item in aiNoList"
              :key="item.algorithmNo"
              :label="item.algorithmName"
              :value="item.algorithmNo"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="接口地址：" prop="apiUrl">
          <el-input
            placeholder="请填写接口地址"
            clearable
            v-model.trim="algorithmForm.apiUrl"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="版本号：" prop="versionNo">
          <el-input
            placeholder="请填写版本号"
            clearable
            v-model.trim="algorithmForm.versionNo"
            maxlength="10"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div>
        <!-- 按钮 -->
        <div style="text-align: center">
          <el-button @click="handleClose">{{ t('common.cancel') }}</el-button>
          <el-button type="primary" :disabled="submit" @click="submitForm(algorithmFormRef)">{{
            t('common.ok')
          }}</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script setup>
const { t } = useI18n()
import { ref, toRefs, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import useMessage from '@/utils/useMessage'
import { addAlgorithmInfoApi, editAlgorithmInfoApi } from '@/api/RuleManage/ImageReviewAlgorithm'

import { object } from 'vue-types'
const message = useMessage()
// 获取路由器实例
const router = useRouter()
// route响应式对象，监控变化，传值
const route = useRoute()

/** 接收父级传入的参数 start */
const props = defineProps({
  handleRule: {
    type: Boolean,
    default: false, //默认值
    required: true //是否必传
  },
  edit: {
    type: Boolean,
    default: false, //默认值
    required: true //是否必传
  },
  algorithmInfo: {
    type: Object,
    default: () => {}, //默认值
    required: false //是否必传
  },
  auditUrl: {
    type: String,
    default: ''
  },
  aiNoList: {
    type: Array,
    default: () => [], //默认值
    required: false //是否必传
  },
  aiHandleList: {
    type: Array,
    default: () => [], //默认值
    required: false //是否必传
  },
  algorithmTypeList: {
    type: Array,
    default: () => [], //默认值
    required: false //是否必传
  }
})

// 解构传入的弹窗开关
const { handleRule, edit, algorithmInfo, auditUrl } = toRefs(props)
// 接收关闭弹窗自定义方法
const emits = defineEmits(['closeDialog'])
// 关闭删除角色弹窗
const handleClose = (done) => {
  emits('closeDialog')
}
/** 接收父级传入的参数 end */

/**  loading加载开始  start */
const loading = ref(false)
const pending = ref([])
const cancelLoading = (add) => {
  for (const p in pending.value) {
    if (pending.value[p].conf === add) {
      pending.value.splice(p, 1) // 把这条记录从数组中移除
    }
  }
  loading.value = pending.value.length > 0
}
const addLoading = (add) => {
  loading.value = true
  pending.value.push({ conf: add })
}
/**  loading加载开始  end */

const submit = ref(false)
const isHandle = ref(false)
const algorithmFormRef = ref() // 表单校验
const algorithmNo = ref('') //审核项编号
const algorithmForm = reactive({
  // algorithmForm: {
  algorithmType: 'audit', // 类型
  algorithmNo: '', // 算法编号
  algorithmName: '', // 算法名称
  algorithmDesc: '', // 算法描述
  apiUrl: '', // 接口地址
  versionNo: '' //版本号
  // }
})
const rules = reactive({
  algorithmType: [{ required: true, message: '请选择类型' }],
  algorithmNo: [
    { required: true, message: '请选择算法编号' },
    {
      validator: (rule, value, callback) => {
        if (/[^0-9_,a-zA-Z-]/.test(value)) {
          return callback(new Error('仅可输入英文和数字'))
        } else {
          callback()
        }
      }
    }
  ],
  algorithmName: [
    { required: true, message: '请填写算法名称' },
    { min: 1, max: 40, message: '长度在 1 到 40 个字符' }
  ],
  algorithmDesc: [{ min: 1, max: 200, message: '长度在 1 到 200 个字符' }],
  versionNo: [
    { required: true, message: '请填写版本号' },
    { min: 1, max: 10, message: '长度在 1 到 10 个字符' },
    {
      validator: (rule, value, callback) => {
        // /^\d+$|^\d*\.\d+$/g.test(value)
        const reg = /^(?=.*\d)\d+(\.\d+)*$/ // 版本号只允许输入数字和.
        if (!reg.test(value)) {
          return callback(
            new Error(`版本号只允许输入数字和小数点，且小数点前后要有数字，请重新输入`)
          )
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  apiUrl: [
    { required: true, message: '请填写接口地址' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符' },
    {
      validator: (rule, value, callback) => {
        if (
          !/(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&amp;:/~\+#]*[\w\-\@?^=%&amp;/~\+#])?/.test(
            value
          )
        ) {
          return callback(new Error('输入的接口地址不符合URL格式，无法保存'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
})

onMounted(() => {
  algorithmForm.apiUrl = auditUrl.value
  if (edit.value) {
    const data = algorithmInfo.value

    algorithmForm.algorithmId = data.algorithmId // 主键
    algorithmForm.algorithmName = data.algorithmName // 名称
    algorithmForm.algorithmDesc = data.algorithmDesc // 描述
    algorithmForm.algorithmType = data.algorithmType // 类型
    algorithmForm.apiUrl = data.apiUrl // 接口地址
    algorithmForm.versionNo = data.versionNo // 版本号
    algorithmForm.algorithmNo = data.algorithmNo
  }
})

// 提交校验
const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      if (edit.value) {
        editAlgorithmInfo()
      } else {
        addAlgorithmInfo()
      }
    } else {
      return false
    }
  })
}
const handleChange = (value) => {
  algorithmForm.algorithmNo = ''
}

//更新算法
const editAlgorithmInfo = async () => {
  const data = {
    algorithmId: algorithmForm.algorithmId,
    algorithmType: algorithmForm.algorithmType,
    algorithmName: algorithmForm.algorithmName,
    algorithmDesc: algorithmForm.algorithmDesc,
    algorithmNo: algorithmForm.algorithmNo,
    apiUrl: algorithmForm.apiUrl,
    versionNo: algorithmForm.versionNo
  }
  submit.value = true
  try {
    const res = await editAlgorithmInfoApi(data)
    if (res.code == 0) {
      message.success('编辑成功')
      emits('closeDialog', true)
    }
  } finally {
    submit.value = false
  }
}

// 新增算法
const addAlgorithmInfo = async () => {
  const data = {
    algorithmType: algorithmForm.algorithmType,
    algorithmName: algorithmForm.algorithmName,
    algorithmDesc: algorithmForm.algorithmDesc,
    algorithmNo: algorithmForm.algorithmNo,
    apiUrl: algorithmForm.apiUrl,
    versionNo: algorithmForm.versionNo
  }
  submit.value = true
  try {
    const res = await addAlgorithmInfoApi(data)
    if (res.code == 0) {
      message.success('创建成功')
      emits('closeDialog', true)
    }
  } finally {
    submit.value = false
  }
}
</script>

<style scope lang="less">
.ai-rules-dlg-wrap {
  // font-size: 13px;
  :deep(.el-dialog) {
    margin-top: 5vh !important;
    min-width: 720px;
  }
  :deep(.el-textarea__inner) {
    padding-right: 55px;
  }
}
.el-form-item__threshold {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  top: 100%;
  left: 0;
}
</style>
