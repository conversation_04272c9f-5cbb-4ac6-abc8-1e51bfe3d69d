<template>
  <!-- 删除角色弹窗 -->
  <div class="m-dlt-role-dlg">
    <el-dialog
      title="删除提示"
      v-model="deleteRule"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <div>确定要删除规则吗？ </div>
      <!-- 按钮 -->
      <template #footer>
        <div class="dialog-footer text-c">
          <el-button @click="handleClose">{{ t('common.cancel') }}</el-button>
          <el-button
            :loading="loading"
            type="primary"
            style="margin-left: 60px"
            @click="handleDeleteRule()"
            >{{ t('common.ok') }}</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
const { t } = useI18n()
import { ref, toRefs, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
// import $http from '@/utils/request'
import { deleteAiRuleApi } from '@/api/RuleManage'
import useMessage from '@/utils/useMessage'

import { object } from 'vue-types'
const message = useMessage()
// 获取路由器实例
const router = useRouter()
// route响应式对象，监控变化，传值
const route = useRoute()

/** 接收父级传入的参数 start */
const props = defineProps({
  deleteRule: {
    type: Boolean,
    default: false, //默认值
    required: true //是否必传
  },
  dataInfo: {
    type: Object,
    default: () => {}, //默认值
    required: false //是否必传
  }
})
// 解构传入的弹窗开关
const { deleteRule, dataInfo } = toRefs(props)
// 接收关闭弹窗自定义方法
const emits = defineEmits(['closeDialog'])
// 关闭删除角色弹窗
const handleClose = (done) => {
  emits('closeDialog')
}
/** 接收父级传入的参数 end */

/**  loading加载开始  start */
const loading = ref(false)
const pending = ref([])
const cancelLoading = (add) => {
  for (const p in pending.value) {
    if (pending.value[p].conf === add) {
      pending.value.splice(p, 1) // 把这条记录从数组中移除
    }
  }
  loading.value = pending.value.length > 0
}
const addLoading = (add) => {
  loading.value = true
  pending.value.push({ conf: add })
}
/**  loading加载开始  end */

const submit = ref(false)
const aiRuleId = ref('') // 主键id

onMounted(() => {
  aiRuleId.value = dataInfo.value.aiRuleId
})
// 删除
// const handleDeleteRule = () => {
//   // dialogVisible.value = false
//   let data = {
//     aiRuleId: aiRuleId.value
//   }
//   $http
//     .post({
//       url: `/review/aiRule/delete`,
//       data,
//       router,
//       type: 'form'
//     })
//     .then((res) => {
//       if (res.code == 0) {
//         message.success('删除成功')
//         emits('closeDialog', true)
//       }
//     })
//     .catch((err) => {})
// }

// 删除 deleteAiRuleApi
const handleDeleteRule = async () => {
  // dialogVisible.value = false
  const data = {
    aiRuleId: aiRuleId.value
  }
  try {
    const res = await deleteAiRuleApi(data)
    if (res.code == 0) {
      message.success('删除成功')
      emits('closeDialog', true)
    }
  } finally {
  }
}
</script>
<style scoped lang="less">
.m-dlt-role-dlg {
  :deep(.el-dialog) {
    width: 600px;
  }
}
</style>
