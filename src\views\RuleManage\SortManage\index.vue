<!-- eslint-disable vue/v-on-event-hyphenation -->
<template>
  <!-- 分类管理 -->
  <UmvContent>
    <div class="h-full !flex flex-col">
      <!-- 查询条件 -->
      <UmvQuery
        v-model="searchData"
        :opts="queryOpts"
        label-width="90px"
        @check="handleSearch"
        @reset="handleReset"
      />

      <!-- 表格 -->
      <UmvTable
        v-loading="loading"
        :data="tableData"
        :columns="columns"
        @refresh="queryCategoryList"
      >
        <!-- 工具栏 -->
        <template #tools>
          <el-button type="primary" size="small" v-track:click.btn @click="openDialog('add')">
            {{ t('common.add') }}
          </el-button>
        </template>

        <!-- 分页 -->
        <template #pagination>
          <Pagination
            background
            :current-page="query.pageNo"
            :page-size="query.pageSize"
            :total="query.total"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          />
        </template>
      </UmvTable>
    </div>

    <!-- 编辑弹窗 -->
    <sortDlg
      v-if="handleRule"
      :handleRule="handleRule"
      :edit="edit"
      :dataInfo="dataInfo"
      @closeDialog="closeDialog"
    />
    <!-- 删除分类弹窗 -->
    <deleteSortDlg
      v-if="deleteRule"
      :deleteRule="deleteRule"
      :dataInfo="dataInfo"
      @closeDialog="closeDialog"
    />
  </UmvContent>
</template>

<script setup lang="tsx">
import { ref, reactive, onMounted } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
import { ElInput, ElButton } from 'element-plus'

// 组件导入
import UmvContent from '@/components/UmvContent'
import UmvTable from '@/components/UmvTable'
import { UmvQuery, type QueryOption } from '@/components/UmvQuery'
import type { TableColumn } from '@/components/UmvTable/src/types'
import sortDlg from './components/sortDlg.vue'
import deleteSortDlg from './components/deleteSortDlg.vue'

// API 和工具函数
import { getCategoryList } from '@/api/RuleManage'

// 定义组件名称
defineOptions({
  name: 'SortManage'
})

const { t } = useI18n()

const loading = ref(false)
const handleRule = ref(false) // 添加分类弹窗
const deleteRule = ref(false) // 删除弹窗
const edit = ref(false) // 是否为编辑
const tableData = ref([]) // 数据列表
const query = reactive({
  total: 0,
  pageNo: 1,
  pageSize: 10
})
const searchData = ref({
  categoryName: ''
})
const dataInfo = ref<any>({})

// 查询配置
const queryOpts = ref<Record<string, QueryOption>>({
  categoryName: {
    label: '分类名称',
    defaultVal: '',
    controlRender: (form: any) => (
      <ElInput
        v-model={form.categoryName}
        maxlength="40"
        clearable
        placeholder="请输入分类名称"
        style="width: 100%"
      />
    )
  }
})

// 表格列配置
const columns = ref<TableColumn[]>([
  {
    prop: 'index',
    label: '序号',
    width: '60px',
    renderTemplate: (scope) => <span>{scope.$index + 1}</span>
  },
  { prop: 'categoryName', label: '分类名称', minWidth: '150px' },
  { prop: 'remark', label: '描述', minWidth: '150px' },
  { prop: 'auditNum', label: '审核项数量', minWidth: '120px' },
  {
    prop: 'operation',
    label: '操作',
    fixed: 'right',
    width: '110px',
    renderTemplate: (scope) => (
      <div>
        <ElButton type="primary" link onClick={() => openDialog('edit', scope.row)}>
          {t('common.edit')}
        </ElButton>
        <ElButton type="danger" link onClick={() => openDialog('delete', scope.row)}>
          {t('common.delete')}
        </ElButton>
      </div>
    )
  }
])

// 打开添加角色弹窗
const openDialog = (type: string, obj?: any) => {
  console.log(obj, type)
  switch (type) {
    // 新增
    case 'add':
      handleRule.value = true
      edit.value = false
      break
    // 编辑
    case 'edit':
      edit.value = true
      dataInfo.value = obj
      handleRule.value = true
      break
    // 删除
    case 'delete':
      dataInfo.value = obj
      deleteRule.value = true
      break
  }
}
// 关闭添加角色弹窗
const closeDialog = (result?: any) => {
  edit.value = false
  handleRule.value = false
  deleteRule.value = false
  if (result) {
    queryCategoryList()
  }
}
// 获取分类列表
const queryCategoryList = async () => {
  const data = {
    categoryName: searchData.value.categoryName,
    pageNum: query.pageNo,
    pageSize: query.pageSize
  }
  loading.value = true
  try {
    const resData = await getCategoryList(data)
    tableData.value = resData?.list || []
    query.total = resData?.total || 0
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  queryCategoryList()
})

// 搜索
const handleSearch = () => {
  queryCategoryList()
}
// 重置
const handleReset = () => {
  searchData.value.categoryName = ''
  //queryCategoryList()
}

// 改变页数
const handlePageChange = (val: number) => {
  query.pageNo = val
  // queryCategoryList()
}
// 改变条数
const handleSizeChange = (val: number) => {
  query.pageNo = 1
  query.pageSize = val
  // queryCategoryList()
}
</script>
