<!-- eslint-disable vue/require-valid-default-prop -->
<template>
  <div class="ai-rules-dlg-wrap">
    <el-dialog
      :title="edit ? t('common.edit') : t('common.add')"
      v-model="handleRule"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleFormRef"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item v-if="edit" label="审核项编号：">
          <span>{{ auditServerNo }}</span>
        </el-form-item>
        <el-form-item label="审核项：" prop="ruleName">
          <el-input
            placeholder="请输入审核项"
            clearable
            v-model.trim="ruleForm.ruleName"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="分类：" prop="categoryId">
          <el-select
            width="100%"
            v-model="ruleForm.categoryId"
            placeholder="请选择分类"
            show-word-limit
          >
            <el-option
              v-for="item in ruleClassList"
              :key="item.categoryId"
              :label="item.categoryName"
              :value="item.categoryId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="描述：" prop="remark">
          <el-input
            placeholder="请输入描述"
            clearable
            v-model.trim="ruleForm.remark"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="准确度：" prop="ruleAccuracy">
          <el-row>
            <el-col :span="15">
              <el-input
                placeholder="请输入准确度"
                clearable
                v-model.trim="ruleForm.ruleAccuracy"
                maxlength="2"
                show-word-limit
              />
            </el-col>
            <el-col :span="2" style="margin-left: 10px">
              <span>%</span>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="算法：" prop="algorithmId">
          <el-select width="100%" v-model="ruleForm.algorithmId" placeholder="请选择算法" clearable>
            <el-option
              v-for="item in arithmeticList"
              :key="item.algorithmId"
              :label="`${item.algorithmId}-${item.algorithmName}`"
              :value="item.algorithmId"
              @click="selectArithmetic(item)"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="aiRuleLimit" label="默认阈值：">
          <el-slider v-model="ruleForm.aiRuleLimit" />
        </el-form-item>
        <el-form-item v-if="ruleForm.multiLimit == 1" prop="thresholdReplenish" label="多人阈值：">
          <el-slider v-model="ruleForm.thresholdReplenish" :min="2" />
        </el-form-item>
        <el-form-item label="期望结果：" prop="expectResult">
          <el-radio-group v-model="ruleForm.expectResult">
            <el-radio :label="0">是</el-radio>
            <el-radio :label="1">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="返回原因：" prop="showReason">
          <el-input
            placeholder="请输入返回原因"
            clearable
            v-model.trim="ruleForm.showReason"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div>
        <!-- 按钮 -->
        <div style="text-align: center">
          <el-button @click="handleClose">{{ t('common.cancel') }}</el-button>
          <el-button type="primary" :disabled="submit" @click="submitForm(ruleFormRef)">{{
            t('common.ok')
          }}</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script setup>
const { t } = useI18n()
import { ref, toRefs, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
// import $http from '@/utils/request'
import { getAiComputedList, editAiRuleInfoApi, addAiRuleInfoApi } from '@/api/RuleManage'
import useMessage from '@/utils/useMessage'

import { object } from 'vue-types'
const message = useMessage()
// 获取路由器实例
const router = useRouter()
// route响应式对象，监控变化，传值
const route = useRoute()

/** 接收父级传入的参数 start */
const props = defineProps({
  handleRule: {
    type: Boolean,
    default: false, //默认值
    required: true //是否必传
  },
  edit: {
    type: Boolean,
    default: false, //默认值
    required: true //是否必传
  },
  ruleInfo: {
    type: Object,
    default: () => {}, //默认值
    required: false //是否必传
  },
  ruleClassList: {
    type: Array,
    default: () => [], //默认值
    required: false //是否必传
  }
})
// 解构传入的弹窗开关
const { handleRule, edit, ruleInfo } = toRefs(props)
// 接收关闭弹窗自定义方法
const emits = defineEmits(['closeDialog'])
// 关闭删除角色弹窗
const handleClose = (done) => {
  emits('closeDialog')
}
/** 接收父级传入的参数 end */

/**  loading加载开始  start */
const loading = ref(false)
const pending = ref([])
const cancelLoading = (add) => {
  for (const p in pending.value) {
    if (pending.value[p].conf === add) {
      pending.value.splice(p, 1) // 把这条记录从数组中移除
    }
  }
  loading.value = pending.value.length > 0
}
const addLoading = (add) => {
  loading.value = true
  pending.value.push({ conf: add })
}
/**  loading加载开始  end */

const submit = ref(false)
const ruleFormRef = ref() // 表单校验
const aiRuleLimit = ref() //滑块数值
const arithmeticList = ref([]) //算法列表
const auditServerNo = ref('') //审核项编号
const algorithmNo = ref('') //算法编号
const aiRuleId = ref('') //AI审核项主键
const ruleForm = reactive({
  categoryId: '', // 分类
  algorithmId: '', //算法id
  ruleName: '', // 审核项
  remark: '', // 说明
  ruleAccuracy: '', // 准确度
  aiRuleLimit: 50, //阈值1
  expectResult: 0, //期望结果
  showReason: '', //返回原因
  thresholdReplenish: '', //多人阈值
  multiLimit: ''
})
const rules = reactive({
  categoryId: [{ required: true, message: '请选择分类' }],
  ruleName: [
    { required: true, message: '请填写审核项' },
    { min: 1, max: 20, message: '长度在 1 到 20 个字符' }
  ],
  remark: [{ min: 1, max: 200, message: '长度在 1 到 200 个字符' }],
  ruleAccuracy: [
    { required: true, message: '请输入准确度' },
    {
      validator: (rule, value, callback) => {
        if (!/(^(\d|[1-9]\d)(\.\d{1,2})?$)|(^100$)/.test(value)) {
          return callback(new Error('只允许输入0-100数字，最多两位小数'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  algorithmId: [{ required: true, message: '请选择算法' }],
  thresholdReplenish: [{ required: true, message: '请选择多人阈值' }],
  aiRuleLimit: [{ required: true, message: '请设置阈值' }],
  expectResult: [{ required: true, message: '请选择期望结果' }],
  showReason: [
    { required: true, message: '请填写返回原因' },
    { min: 1, max: 20, message: '长度在 1 到 20 个字符' }
  ]
})

onMounted(() => {
  queryAiComputedList()
  if (edit.value) {
    ruleForm.categoryId = ruleInfo.value.categoryId
    ruleForm.remark = ruleInfo.value.remark
    ruleForm.ruleName = ruleInfo.value.ruleName
    ruleForm.showReason = ruleInfo.value.showReason
    ruleForm.aiRuleLimit = ruleInfo.value.aiRuleLimit
    ruleForm.ruleAccuracy = ruleInfo.value.ruleAccuracy
    ruleForm.expectResult = ruleInfo.value.expectResult
    ruleForm.algorithmId = ruleInfo.value.algorithmId
    ruleForm.thresholdReplenish = ruleInfo.value.thresholdReplenish
    ruleForm.multiLimit = ruleInfo.value.multiLimit
    auditServerNo.value = ruleInfo.value.auditServerNo
    aiRuleId.value = ruleInfo.value.aiRuleId
    algorithmNo.value = ruleInfo.value.algorithmNo
  }
})

// 提交校验
const submitForm = async (formEl) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      if (edit.value) {
        editAiRuleInfo()
      } else {
        addAiRuleInfo()
      }
    } else {
      return false
    }
  })
}
// 获取算法列表
const queryAiComputedList = async () => {
  const data = {
    algorithmType: 'audit'
  }
  loading.value = true
  try {
    const res = await getAiComputedList(data)
    if (res.code == 200 || res.code == 0) {
      arithmeticList.value = res.data
    }
  } finally {
    loading.value = false
  }
}

const selectArithmetic = (obj) => {
  if (ruleForm.multiLimit || ruleForm.thresholdReplenish) {
    ruleForm.multiLimit = ''
    ruleForm.thresholdReplenish = ''
  }
  algorithmNo.value = obj.algorithmNo
  ruleForm.multiLimit = obj.multiLimit
}
// 编辑规则
// const editAiRuleInfo = () => {
//   let data = {
//     ruleName: ruleForm.ruleName,
//     categoryId: ruleForm.categoryId,
//     showReason: ruleForm.showReason,
//     remark: ruleForm.remark,
//     expectResult: ruleForm.expectResult,
//     aiRuleLimit: ruleForm.aiRuleLimit,
//     ruleAccuracy: ruleForm.ruleAccuracy,
//     algorithmId: ruleForm.algorithmId,
//     aiRuleId: aiRuleId.value,
//     algorithmNo: algorithmNo.value
//   }
//   submit.value = true
//   $http
//     .post({
//       url: `/review/aiRule/update`,
//       data,
//       router,
//       type: 'json'
//     })
//     .then((res) => {
//       if (res.code == 0) {
//         message.success('创建成功')
//         emits('closeDialog', true)
//       }
//     })
//     .catch(() => {})
//     .finally(() => {
//       submit.value = false
//     })
// }

// 编辑规则
const editAiRuleInfo = async () => {
  const data = {
    ruleName: ruleForm.ruleName,
    categoryId: ruleForm.categoryId,
    showReason: ruleForm.showReason,
    remark: ruleForm.remark,
    expectResult: ruleForm.expectResult,
    aiRuleLimit: ruleForm.aiRuleLimit,
    ruleAccuracy: ruleForm.ruleAccuracy,
    algorithmId: ruleForm.algorithmId,
    thresholdReplenish: ruleForm.thresholdReplenish,
    multiLimit: ruleForm.multiLimit,
    aiRuleId: aiRuleId.value,
    algorithmNo: algorithmNo.value
  }
  submit.value = true
  try {
    const res = await editAiRuleInfoApi(data)
    if (res.code == 0) {
      message.success('编辑成功')
      emits('closeDialog', true)
    }
  } finally {
    submit.value = false
  }
}

// 新增AI规则
// const addAiRuleInfo = () => {
//   let data = {
//     ruleName: ruleForm.ruleName,
//     categoryId: ruleForm.categoryId,
//     showReason: ruleForm.showReason,
//     remark: ruleForm.remark,
//     expectResult: ruleForm.expectResult,
//     aiRuleLimit: ruleForm.aiRuleLimit,
//     ruleAccuracy: ruleForm.ruleAccuracy,
//     algorithmId: ruleForm.algorithmId,
//     algorithmNo: algorithmNo.value
//   }
//   submit.value = true
//   $http
//     .post({
//       url: `/review/aiRule/save`,
//       data,
//       router,
//       type: 'json'
//     })
//     .then((res) => {
//       if (res.code == 0) {
//         message.success('创建成功')
//         emits('closeDialog', true)
//       }
//     })
//     .catch(() => {})
//     .finally(() => {
//       submit.value = false
//     })
// }

// 新增AI规则
const addAiRuleInfo = async () => {
  const data = {
    ruleName: ruleForm.ruleName,
    categoryId: ruleForm.categoryId,
    showReason: ruleForm.showReason,
    remark: ruleForm.remark,
    expectResult: ruleForm.expectResult,
    aiRuleLimit: ruleForm.aiRuleLimit,
    thresholdReplenish: ruleForm.thresholdReplenish,
    multiLimit: ruleForm.multiLimit,
    ruleAccuracy: ruleForm.ruleAccuracy,
    algorithmId: ruleForm.algorithmId,
    algorithmNo: algorithmNo.value
  }
  submit.value = true
  try {
    const res = await addAiRuleInfoApi(data)
    if (res.code == 0) {
      message.success('创建成功')
      emits('closeDialog', true)
    }
  } finally {
    submit.value = false
  }
}
</script>

<style scope lang="less">
.ai-rules-dlg-wrap {
  // font-size: 13px;
  :deep(.el-dialog) {
    margin-top: 5vh !important;
    min-width: 720px;
  }
  :deep(.el-textarea__inner) {
    padding-right: 55px;
  }
}
.el-form-item__threshold {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  top: 100%;
  left: 0;
}
</style>
